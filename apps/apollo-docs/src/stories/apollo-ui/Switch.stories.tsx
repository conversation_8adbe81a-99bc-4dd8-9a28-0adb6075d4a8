import { useCallback, useState } from "react"
import { ComponentRules, CSSClassesTable, UsageGuidelines } from "@/components"
import { Button, IconButton, Modal, Switch, Typography } from "@apollo/ui"
import { InfoCircle } from "@design-systems/apollo-icons"
import {
  ArgTypes,
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import type { Meta, StoryObj } from "@storybook/react"

/**
 * Switch component
 *
 * The Switch component provides a styled, accessible toggle switch with support for
 * checked, unchecked states, labels, action text, and disabled functionality.
 *
 * Notes:
 * - Default state is unchecked;
 * - Supports labels and action text for better UX;
 * - Built on top of Base UI for accessibility.
 */
const meta = {
  title: "@apollo∕ui/Components/Inputs/Switch",
  component: Switch,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
    design: {
      type: "figma",
      url: "https://www.figma.com/design/5ndGGLsLILgxao32M6rJ0F/Apollo-Design-System?node-id=888-1843&m=dev",
    },
    docs: {
      description: {
        component:
          "The Switch component renders a toggle switch with Apollo design system styling. Supports checked and unchecked states with optional labels and action text.",
      },
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <h3>Import</h3>
          <Source code={`import { Switch } from "@apollo/ui"`} language="tsx" />
          <h2 id="switch-props">Props</h2>
          <ArgTypes />
          <h2 id="switch-usage">Best Practices</h2>
          <UsageGuidelines
            guidelines={[
              "Use switches to: Toggle a single setting or feature on/off",
              "Use switches for immediate actions: changes should take effect immediately without requiring a save action",
              "Keep a positive tone of voice. For example: 'Enable notifications' instead of 'Disable notifications'",
              "Use clear, descriptive labels that explain what the switch controls",
              "Consider using action text to provide additional context about the switch state",
              "Group related switches together with clear section headings",
              "Ensure sufficient spacing between switches for easy interaction",
              "Use disabled state for options that are temporarily unavailable",
              "Switch will always appear with a label when used in forms",
            ]}
          />
          <h2 id="switch-accessibility">Accessibility</h2>
          <UsageGuidelines
            guidelines={[
              <>
                Always provide a visible <code>label</code> prop to ensure the
                switch's purpose is clear to all users.
              </>,
              <>
                Use the <code>disabled</code> prop to disable switches that are
                not currently actionable, ensuring they are not focusable.
              </>,
              "Ensure sufficient color contrast between switch states and background in all conditions.",
              <>
                Use <code>actionText</code> to provide additional context about
                the current state when helpful.
              </>,
            ]}
          />
          <h2 id="switch-css-classes">CSS Classes</h2>
          <CSSClassesTable
            description="The Switch component provides several CSS classes that can be used for custom styling. These classes follow the Apollo design system naming conventions and provide access to different parts and states of the component."
            data={[
              {
                cssClassName: ".ApolloSwitch-fieldRoot",
                description: "Styles applied to the field wrapper",
                usageNotes: "Use for overall component positioning and spacing",
              },
              {
                cssClassName: ".ApolloSwitch-wrapperRoot",
                description: "Styles applied to the switch wrapper element",
                usageNotes: "Target for wrapper-specific styling and layout",
              },
              {
                cssClassName: ".ApolloSwitch-switchRoot",
                description: "Styles applied to the switch input element",
                usageNotes:
                  "Target for switch-specific styling (size, border, background)",
              },
              {
                cssClassName: ".ApolloSwitch-thumbRoot",
                description: "Styles applied to the switch thumb element",
                usageNotes: "Use for thumb styling and animation customization",
              },
              {
                cssClassName: ".ApolloSwitch-actionText",
                description: "Styles applied to the action text element",
                usageNotes:
                  "Use for action text typography and spacing customization",
              },
            ]}
          />

          <h2 id="switch-examples">Examples</h2>
          <Stories title="" />
          <h2 id="switch-dos-donts">Do's and Don'ts</h2>
          <ComponentRules
            rules={[
              {
                positive: {
                  component: (
                    <div
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        gap: 8,
                      }}
                    >
                      <Switch
                        label="Enable email notifications"
                        actionText="On/Off"
                      />
                      <Switch
                        label="Enable push notifications"
                        actionText="On/Off"
                      />
                      <Switch
                        label="Enable SMS notifications"
                        actionText="On/Off"
                      />
                    </div>
                  ),
                  description:
                    "Use clear, specific labels that describe the setting being controlled",
                },
                negative: {
                  component: (
                    <div
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        gap: 8,
                      }}
                    >
                      <Switch label="Option 1" />
                      <Switch label="Option 2" />
                      <Switch label="Option 3" />
                    </div>
                  ),
                  description:
                    "Avoid generic labels that don't explain what the switch controls",
                },
              },
              {
                positive: {
                  component: (
                    <div
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        gap: 24,
                      }}
                    >
                      <Switch label="Dark mode" actionText="On/Off" />
                      <Switch
                        label="Auto-save documents"
                        actionText="Enabled/Disabled"
                      />
                    </div>
                  ),
                  description:
                    "Provide adequate spacing between switches for easy interaction",
                },
                negative: {
                  component: (
                    <div
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        gap: 2,
                      }}
                    >
                      <Switch label="Dark mode" actionText="On/Off" />
                      <Switch
                        label="Auto-save documents"
                        actionText="Enabled/Disabled"
                      />
                    </div>
                  ),
                  description:
                    "Don't place switches too close together - it makes them hard to interact with",
                },
              },
            ]}
          />
        </>
      ),
    },
  },
  argTypes: {
    label: {
      control: { type: "text" },
      description:
        "The label content for the switch. Displayed above the switch.",
      table: { type: { summary: "string" } },
    },
    actionText: {
      control: { type: "text" },
      description:
        "Text displayed next to the switch to indicate its state (e.g., 'On/Off').",
      table: { type: { summary: "string" } },
    },
    checked: {
      control: { type: "boolean" },
      description: "Controls the checked state of the switch.",
      table: { type: { summary: "boolean" } },
    },
    defaultChecked: {
      control: { type: "boolean" },
      description: "The default checked state (uncontrolled).",
      table: { type: { summary: "boolean" } },
    },
    disabled: {
      control: { type: "boolean" },
      description: "Disables the switch.",
      table: { type: { summary: "boolean" } },
    },
    required: {
      control: { type: "boolean" },
      description: "Whether the switch is required in a form.",
      table: { type: { summary: "boolean" } },
    },
    labelDecorator: {
      control: false,
      description: "Additional content to display next to the label.",
      table: { type: { summary: "ReactNode" } },
    },
    onCheckedChange: {
      control: false,
      description: "Callback fired when the switch state changes.",
      table: {
        type: {
          summary: "(checked: boolean, event: Event) => void",
        },
      },
    },
    ref: {
      control: false,
      description: "Ref for the underlying button element.",
      table: { type: { summary: "Ref<HTMLButtonElement>" } },
    },
    className: {
      control: { type: "text" },
      description: "Additional CSS class names to apply to the root element.",
      table: { type: { summary: "string" } },
    },
    fieldProps: {
      control: false,
      description: "Additional props to pass to the Field component.",
      table: { type: { summary: "FieldProps" } },
    },
  },
  args: {
    label: "Switch",
    defaultChecked: false,
  },
} satisfies Meta<typeof Switch>

export default meta

type Story = StoryObj<typeof Switch>

/** Default Switch (demonstrates basic functionality) */
export const Overview: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Overview Switch with default settings. The component defaults to unchecked state with an optional label.",
      },
    },
  },
  args: {
    label: "Default Switch",
    actionText: "On/Off",
  },
}

/** Switch without labels */
export const WithoutLabels: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Switches without labels for use in compact layouts or when context is clear.",
      },
    },
  },
  render: (args) => (
    <div style={{ display: "flex", gap: 16, alignItems: "center" }}>
      <Switch {...args} />
      <Switch {...args} checked />
      <Switch {...args} disabled />
    </div>
  ),
  args: {
    label: undefined,
    actionText: undefined,
  },
}

/** Switch with label and label decorator */
export const LabelAndRequired: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Switches with required label and label decorators for additional context.",
      },
    },
  },
  render: () => {
    const [open, setOpen] = useState(false)
    return (
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: 16,
          alignItems: "flex-start",
        }}
      >
        <Switch label="Dark Mode" actionText="On/Off" />
        <Switch
          label="Terms and Conditions"
          actionText="Accepted/Not Accepted"
          required
          labelDecorator={
            <IconButton
              type="button"
              onClick={() => setOpen(true)}
              style={{
                padding: 0,
                background: "none",
                height: "fit-content",
                minHeight: "fit-content",
                width: "fit-content",
                minWidth: "fit-content",
                color: "var(--apl-alias-color-background-and-surface-on-surface)",
              }}
              aria-label="More info"
              size="small"
            >
              <InfoCircle size={12}/>
            </IconButton>
          }
        />

        <Modal.Root open={open} onOpenChange={(o) => setOpen(!!o)} dismissible>
          <Modal.Header>Modal Title</Modal.Header>
          <Modal.Content>
            <div>
              Once upon a time, there was a forest where plenty of birds lived
              and built their nests on the trees.
            </div>
          </Modal.Content>
          <Modal.Footer>
            <Button onClick={() => setOpen(false)}>Button</Button>
          </Modal.Footer>
          <Modal.CloseButton />
        </Modal.Root>
      </div>
    )
  },
}


/** Switch disabled states */
export const Disabled: Story = {
  parameters: {
    docs: {
      description: {
        story: "Switches in disabled state across all possible states.",
      },
    },
  },
  render: () => (
    <div
      style={{
        display: "flex",
        gap: 24,
        alignItems: "center",
        flexWrap: "wrap",
      }}
    >
      <Switch disabled label="Disabled Unchecked" actionText="On/Off" />
      <Switch disabled checked label="Disabled Checked" actionText="On/Off" />
      <Switch disabled label="Disabled without action text" />
      <Switch disabled checked />
    </div>
  ),
}

/** Comprehensive states showcase */
export const States: Story = {
  parameters: {
    layout: "padded",
    docs: {
      description: {
        story:
          "A comprehensive showcase of all switch states including default, checked, and disabled variations.",
      },
    },
  },
  render: () => {
    return (
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))",
          gap: 20,
          alignItems: "flex-start",
        }}
      >
        <div style={{ display: "flex", flexDirection: "column", gap: 12 }}>
          <Typography
            level="bodyLarge"
            style={{ fontWeight: "600", marginBottom: 8 }}
          >
            Default States
          </Typography>
          <Switch label="Unchecked" actionText="On/Off" />
          <Switch label="Checked" actionText="On/Off" checked />
          <Switch label="Without action text" />
        </div>

        <div style={{ display: "flex", flexDirection: "column", gap: 12 }}>
          <Typography
            level="bodyLarge"
            style={{ fontWeight: "600", marginBottom: 8 }}
          >
            Disabled States
          </Typography>
          <Switch label="Disabled Unchecked" actionText="On/Off" disabled />
          <Switch
            label="Disabled Checked"
            actionText="On/Off"
            checked
            disabled
          />
          <Switch label="Disabled without text" disabled />
        </div>

        <div style={{ display: "flex", flexDirection: "column", gap: 12 }}>
          <Typography
            level="bodyLarge"
            style={{ fontWeight: "600", marginBottom: 8 }}
          >
            Interactive Examples
          </Typography>
          <Switch label="Email Notifications" actionText="On/Off" />
          <Switch label="Dark Mode" actionText="Enabled/Disabled" />
          <Switch label="Auto-save" actionText="Active/Inactive" checked />
          <Switch label="Privacy Mode" actionText="Private/Public" disabled />
        </div>
      </div>
    )
  },
}

/** Controlled switch example */
export const Controlled: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "A controlled switch that manages its own state with React hooks.",
      },
    },
  },
  render: () => {
    function ControlledDemo() {
      const [checked, setChecked] = useState(false)

      const handleChange = useCallback((isChecked: boolean) => {
        setChecked(isChecked)
      }, [])

      return (
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            gap: 16,
            alignItems: "flex-start",
          }}
        >
          <Switch
            label={`Controlled Switch (${checked ? "On" : "Off"})`}
            actionText="On/Off"
            checked={checked}
            onCheckedChange={handleChange}
          />
          <Typography level="bodySmall" style={{ color: "#6b7280" }}>
            Current state: {checked ? "✓ On" : "○ Off"}
          </Typography>
        </div>
      )
    }
    return <ControlledDemo />
  },
}

/** Switch with action text variations */
export const ActionTextVariations: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Switches with different action text to provide context about the switch state.",
      },
    },
  },
  render: (args) => (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        gap: 16,
        alignItems: "flex-start",
      }}
    >
      <Switch {...args} label="Dark mode" actionText="On/Off" />
      <Switch {...args} label="Auto-save" actionText="Enabled/Disabled" />
      <Switch {...args} label="Notifications" actionText="Active/Inactive" />
      <Switch {...args} label="Privacy mode" actionText="Private/Public" />
    </div>
  ),
}

/** Form integration example */
export const FormIntegration: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Example of switches integrated into a real-world form with submit functionality, loading states, and success feedback.",
      },
    },
  },
  render: () => {
    function FormDemo() {
      const [preferences, setPreferences] = useState({
        emailNotifications: true,
        pushNotifications: false,
        smsNotifications: false,
        darkMode: false,
        autoSave: true,
        privacyMode: false,
      })
      const [isSubmitting, setIsSubmitting] = useState(false)
      const [lastSubmitted, setLastSubmitted] = useState<typeof preferences | null>(null)

      const handlePreferenceChange = useCallback(
        (key: keyof typeof preferences) => {
          return (checked: boolean) => {
            setPreferences((prev) => ({ ...prev, [key]: checked }))
          }
        },
        []
      )

      const handleSubmit = useCallback(async (e: React.FormEvent) => {
        e.preventDefault()
        setIsSubmitting(true)

        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000))

        setLastSubmitted({ ...preferences })
        setIsSubmitting(false)
      }, [preferences])

      return (
        <form
          onSubmit={handleSubmit}
          style={{
            display: "flex",
            flexDirection: "column",
            gap: 24,
            maxWidth: 400,
          }}
        >
          <div>
            <Typography
              level="bodyLarge"
              style={{ fontWeight: "600", marginBottom: 12 }}
            >
              Notification Settings
            </Typography>
            <div style={{ display: "flex", flexDirection: "column", gap: 8 }}>
              <Switch
                label="Email notifications"
                actionText="On/Off"
                checked={preferences.emailNotifications}
                onCheckedChange={handlePreferenceChange("emailNotifications")}
              />
              <Switch
                label="Push notifications"
                actionText="On/Off"
                checked={preferences.pushNotifications}
                onCheckedChange={handlePreferenceChange("pushNotifications")}
              />
              <Switch
                label="SMS notifications"
                actionText="On/Off"
                checked={preferences.smsNotifications}
                onCheckedChange={handlePreferenceChange("smsNotifications")}
              />
            </div>
          </div>

          <div>
            <Typography
              level="bodyLarge"
              style={{ fontWeight: "600", marginBottom: 12 }}
            >
              Application Settings
            </Typography>
            <div style={{ display: "flex", flexDirection: "column", gap: 8 }}>
              <Switch
                label="Dark mode"
                actionText="Enabled/Disabled"
                checked={preferences.darkMode}
                onCheckedChange={handlePreferenceChange("darkMode")}
              />
              <Switch
                label="Auto-save documents"
                actionText="Active/Inactive"
                checked={preferences.autoSave}
                onCheckedChange={handlePreferenceChange("autoSave")}
              />
              <Switch
                label="Privacy mode"
                actionText="Private/Public"
                checked={preferences.privacyMode}
                onCheckedChange={handlePreferenceChange("privacyMode")}
              />
            </div>
          </div>

          <div style={{ borderTop: "1px solid #e5e7eb", paddingTop: 16 }}>
            <Button
              type="submit"
              disabled={isSubmitting}
              style={{ marginBottom: 12 }}
            >
              {isSubmitting ? "Saving..." : "Save Preferences"}
            </Button>

            <Typography
              level="bodySmall"
              style={{ color: "#6b7280" }}
            >
              Active settings: {Object.values(preferences).filter(Boolean).length}{" "}
              of {Object.keys(preferences).length}
            </Typography>

            {lastSubmitted && (
              <Typography
                level="bodySmall"
                style={{ color: "#10b981", marginTop: 8 }}
              >
                ✓ Preferences saved successfully!
              </Typography>
            )}
          </div>
        </form>
      )
    }
    return <FormDemo />
  },
}
